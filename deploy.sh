#!/bin/bash

# News Processing Lambda Deployment Script
# This script packages and deploys the Lambda function

set -e

# Configuration
FUNCTION_NAME="news-processor-lambda"
REGION="us-east-1"  # Change to your preferred region
STACK_NAME="news-processing-stack"

echo "🚀 Starting deployment of News Processing Lambda..."

# Create deployment directory
mkdir -p deployment
cd deployment

# Copy source files
echo "📁 Copying source files..."
cp ../main.py .
cp ../requirements.txt .

# Create virtual environment and install dependencies
echo "📦 Installing dependencies..."
python3 -m venv lambda-env
source lambda-env/bin/activate

# Install dependencies to package directory
pip install --target ./package -r requirements.txt

# Copy main function to package
cp main.py package/

# Create deployment package
echo "📦 Creating deployment package..."
cd package
zip -r ../deployment-package.zip .
cd ..

# Deploy CloudFormation stack
echo "☁️ Deploying CloudFormation stack..."
aws cloudformation deploy \
  --template-file ../cloudformation-template.yaml \
  --stack-name $STACK_NAME \
  --parameter-overrides LambdaFunctionName=$FUNCTION_NAME \
  --capabilities CAPABILITY_NAMED_IAM \
  --region $REGION

# Update Lambda function code
echo "🔄 Updating Lambda function code..."
aws lambda update-function-code \
  --function-name $FUNCTION_NAME \
  --zip-file fileb://deployment-package.zip \
  --region $REGION

# Wait for function to be updated
echo "⏳ Waiting for function update to complete..."
aws lambda wait function-updated \
  --function-name $FUNCTION_NAME \
  --region $REGION

echo "✅ Deployment completed successfully!"
echo "📊 Function details:"
aws lambda get-function \
  --function-name $FUNCTION_NAME \
  --region $REGION \
  --query 'Configuration.[FunctionName,Runtime,Timeout,MemorySize,LastModified]' \
  --output table

echo ""
echo "🔧 To test the function manually:"
echo "aws lambda invoke --function-name $FUNCTION_NAME --region $REGION response.json"

echo ""
echo "📅 Schedule Details:"
echo "The function is scheduled to run daily at 6:00 AM UTC (11:30 AM IST)"
echo "This is 30 minutes after your Google Alerts script completes"

# Cleanup
deactivate
cd ..
rm -rf deployment

echo "🎉 Deployment script completed!"