AWSTemplateFormatVersion: '2010-09-09'
Description: 'News Processing Lambda Function with Scheduled Trigger'

Parameters:
  LambdaFunctionName:
    Type: String
    Default: 'news-processor-lambda'
    Description: 'Name for the Lambda function'
  
  ScheduleExpression:
    Type: String
    Default: 'cron(0 6 * * ? *)'  # 6:00 AM UTC daily (11:30 AM IST)
    Description: 'CloudWatch Events schedule expression'

Resources:
  # IAM Role for Lambda
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub '${LambdaFunctionName}-execution-role'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: S3AccessPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - 'arn:aws:s3:::sen-news'
                  - 'arn:aws:s3:::sen-news/*'

  # Lambda Function
  NewsProcessorLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Ref LambdaFunctionName
      Runtime: python3.9
      Handler: main.lambda_handler
      Role: !GetAtt LambdaExecutionRole.Arn
      Code:
        ZipFile: |
          # Placeholder - replace with actual deployment package
          def lambda_handler(event, context):
              return {'statusCode': 200, 'body': 'Hello World'}
      Timeout: 900  # 15 minutes
      MemorySize: 512
      Environment:
        Variables:
          AWS_ACCESS_KEY: '********************'
          AWS_SECRET_KEY: '3ChDbvjNTc9tqI4CR5S7teD1RBKVmfecA7MsLSW9'
          S3_BUCKET_NAME: 'sen-news'
          API_URL: 'http://*************:8000/api/v1/create-articles'
          WEBHOOK_URL: 'https://sustainabilityeconomics736.webhook.office.com/webhookb2/09b09f77-9689-4d6e-ab7b-7d3ada9eabcc@a8606430-fd02-4b66-b04e-e614589bb5ae/IncomingWebhook/6863eaf995d6449aba88b297813fe5d4/edf64186-ced7-41af-8555-dcbd1cb36c0a'

  # CloudWatch Events Rule
  ScheduledRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${LambdaFunctionName}-schedule'
      Description: 'Trigger news processing after Google Alerts completes'
      ScheduleExpression: !Ref ScheduleExpression
      State: ENABLED
      Targets:
        - Arn: !GetAtt NewsProcessorLambda.Arn
          Id: NewsProcessorTarget

  # Permission for CloudWatch Events to invoke Lambda
  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref NewsProcessorLambda
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt ScheduledRule.Arn

  # CloudWatch Log Group
  LambdaLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/lambda/${LambdaFunctionName}'
      RetentionInDays: 14

Outputs:
  LambdaFunctionArn:
    Description: 'ARN of the Lambda function'
    Value: !GetAtt NewsProcessorLambda.Arn
    Export:
      Name: !Sub '${AWS::StackName}-LambdaArn'
  
  ScheduleRuleArn:
    Description: 'ARN of the CloudWatch Events rule'
    Value: !GetAtt ScheduledRule.Arn
    Export:
      Name: !Sub '${AWS::StackName}-ScheduleRuleArn'