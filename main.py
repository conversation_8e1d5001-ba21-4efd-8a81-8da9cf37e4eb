import json
import boto3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz
import requests
import pymsteams
from typing import List, Dict, Any
import os

# AWS Configuration
aws_access_key = os.environ.get('AWS_ACCESS_KEY', '********************')
aws_secret_key = os.environ.get('AWS_SECRET_KEY', '3ChDbvjNTc9tqI4CR5S7teD1RBKVmfecA7MsLSW9')
bucket_name = os.environ.get('S3_BUCKET_NAME', 'sen-news')

# API Configuration
API_URL = os.environ.get('API_URL', 'http://43.205.11.142:8000/api/v1/create-articles')
WEBHOOK_URL = os.environ.get('WEBHOOK_URL', 'https://sustainabilityeconomics736.webhook.office.com/webhookb2/09b09f77-9689-4d6e-ab7b-7d3ada9eabcc@a8606430-fd02-4b66-b04e-e614589bb5ae/IncomingWebhook/6863eaf995d6449aba88b297813fe5d4/edf64186-ced7-41af-8555-dcbd1cb36c0a')

# Add your VALID_LABELS list here - keeping as comment for you to add
# VALID_LABELS = [your labels list]

def get_latest_json_file():
    """Get the latest JSON file from S3 bucket"""
    try:
        s3 = boto3.client('s3', 
                         aws_access_key_id=aws_access_key, 
                         aws_secret_access_key=aws_secret_key)
        
        # List all objects in the google-alerts/jsons/ folder
        response = s3.list_objects_v2(
            Bucket=bucket_name,
            Prefix='google-alerts/jsons/'
        )
        
        if 'Contents' not in response:
            raise Exception("No files found in S3 bucket")
        
        # Sort by LastModified to get the latest file
        latest_file = max(response['Contents'], key=lambda x: x['LastModified'])
        return latest_file['Key']
        
    except Exception as e:
        print(f"Error getting latest JSON file: {str(e)}")
        raise

def get_labels_recursive(labels):
    """Main processing function - keeping your original logic"""
    try:
        # Get the latest file instead of hardcoded one
        file_key = get_latest_json_file()
        print(f"Processing file: {file_key}")
        
        s3 = boto3.client('s3', 
                         aws_access_key_id=aws_access_key, 
                         aws_secret_access_key=aws_secret_key)
        
        response = s3.get_object(Bucket=bucket_name, Key=file_key)
        json_content = json.loads(response['Body'].read().decode('utf-8'))
        data = pd.DataFrame(json_content)
        
        # Your original processing logic
        Sector = []
        final_scores = []
        threshold = 0.1
        current_labels = labels
        
        for _, i in data.iterrows():
            text = i.body + ' ' + i.url + " " + i.keyword
            final_label = None
            final_score = None
            
            try:
                final_score = 1
                final_label = get_label(text)  # Your existing get_label function
                print(i.keyword, final_label)
                
                temp = []
                final_label['labels'] = list(set(final_label['labels']))
                
                for label in final_label['labels']:
                    temp.append({
                        'Title': i.title, 
                        'Url': i.url, 
                        'Sector': label, 
                        'importance_score': final_score
                    })
                Sector += temp
                
            except Exception as e:
                print(f"Error processing item: {str(e)}")
                continue
        
        push_data = pd.DataFrame(Sector)
        push_data.importance_score = push_data.importance_score.round(2)
        return push_data
        
    except Exception as e:
        print(f"Error in get_labels_recursive: {str(e)}")
        raise

def get_label(text):
    """Your existing get_label function - add your implementation here"""
    # Add your complete get_label function implementation here
    # This is where your LangChain logic goes
    pass

def send_message(notebook_type):
    """Send notification to Microsoft Teams"""
    try:
        ist_time = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('Asia/Kolkata'))
        formatted_time = ist_time.strftime('%Y-%m-%d : : %H') + "H"
        
        message = pymsteams.connectorcard(WEBHOOK_URL)
        message.color("#F8C471")
        
        if notebook_type == 'Sustainability Economics News':
            message.title("Google Alerts")
            message.text(f"New articles are available for {formatted_time}")
            message.addLinkButton("Open SenTool", "http://sentool.duckdns.org:3000/login/")
        else:
            message.title("RSS Feeds")
            message.text(f"New articles are available for {formatted_time}")
            message.addLinkButton(
                "Open One-Note",
                "https://sustainabilityeconomics736-my.sharepoint.com/:o:/g/personal/alerts_sustainabilityeconomicsnews_com/Emmz3lh7sgZNuo5DQI1vNuABnd43iBHQ_kpRroaDbEJ8xg?e=4Btigh"
            )
        
        message.send()
        print("✅ Microsoft Teams message sent successfully.")
        
    except Exception as e:
        print(f"❌ Failed to send Teams message: {str(e)}")

def post_to_api(df):
    """Post processed data to API"""
    headers = {
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
        'Content-Type': 'application/json',
        "accept-language": "en-US,es;q=0.8"
    }
    
    # Generate date-hour for API
    ist_time = datetime.utcnow().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('Asia/Kolkata'))
    date_hour = ist_time.strftime('%Y-%m-%d-%H')
    
    try:
        for name, group in df.groupby('Sector'):
            print(f"📌 Sector: {name} | Articles: {len(group)}")
            print("=" * 80)
            
            for _, row in group.iterrows():
                payload = {
                    "date": f"{date_hour}H",
                    "title": row['Title'],
                    "link": row['Url'],
                    "sector": row['Sector'],
                    "score": row['importance_score'],
                    "priority": None,
                    "assignee": None,
                    "status": "Not Started",
                    "comments": " ",
                    "published": "No"
                }
                
                response = requests.post(API_URL, headers=headers, data=json.dumps(payload))
                print(response.text)
                print("Done & Dusted".center(100, "-"))
                
    except Exception as e:
        print(f"Error posting to API: {str(e)}")
        raise

def lambda_handler(event, context):
    """Main Lambda handler function"""
    try:
        print("🚀 Starting news processing...")
        
        # Check if this is a scheduled trigger (from EventBridge)
        if event.get('source') == 'aws.events':
            print("📅 Triggered by scheduled event")
        
        # Your labels list - add your complete labels here
        labels = []  # Add your complete labels list here
        
        # Process the data using your existing logic
        print("📊 Processing JSON data...")
        result_df = get_labels_recursive(labels)
        
        if result_df.empty:
            print("⚠️ No data to process")
            return {
                'statusCode': 200,
                'body': json.dumps({'message': 'No data to process'})
            }
        
        # Clean and prepare data
        df = result_df.copy()
        df.dropna(subset=["Title"], inplace=True)
        df.reset_index(drop=True, inplace=True)
        
        print(f"📈 Found {len(df)} articles to process")
        
        # Post to API
        print("🌐 Posting to API...")
        post_to_api(df)
        
        # Send Teams notification
        print("📢 Sending Teams notification...")
        send_message("Sustainability Economics News")
        
        print("✅ Processing completed successfully!")
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Processing completed successfully',
                'articles_processed': len(df)
            })
        }
        
    except Exception as e:
        print(f"❌ Error in lambda_handler: {str(e)}")
        
        # Send error notification
        try:
            error_message = pymsteams.connectorcard(WEBHOOK_URL)
            error_message.color("#FF0000")
            error_message.title("⚠️ News Processing Error")
            error_message.text(f"Error occurred during processing: {str(e)}")
            error_message.send()
        except:
            pass
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }